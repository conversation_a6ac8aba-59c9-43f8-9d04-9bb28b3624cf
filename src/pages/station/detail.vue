<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import type { Station, Inverter } from '@/types/api/Station'
import { getStationByStationCode, getInverterList } from '@/api/station'
import StationInfo from './components/StationInfo.vue'

function goToInverterDetail(inverter: Inverter) {
  if (!inverter.inverterSn) return
  uni.navigateTo({
    url: `/pages/station/inverter?inverterSn=${inverter.inverterSn}`,
    success: (res: any) => {
      // 通过EventChannel传递inverter数据
      if (res.eventChannel) {
        console.log(inverter)
        res.eventChannel.emit('receiveInverterData', { inverter })
      }
    },
  })
}

const stationCode = ref('')
const station = ref<Station>({})
const inverterList = ref<Inverter[]>([])

async function fetchStationData() {
  if (!stationCode.value) return
  try {
    const [stationDetail, inverters] = await Promise.all([
      getStationByStationCode({ stationCode: stationCode.value }),
      getInverterList({ stationCode: stationCode.value }),
    ])
    station.value = stationDetail
    inverterList.value = inverters
  }
  catch (error) {
    console.error('Failed to fetch station data:', error)
  }
}

onLoad((options) => {
  if (options?.stationCode) {
    stationCode.value = options.stationCode
    fetchStationData()
  }
})

</script>

<template>
  <view class="station-detail-page">
    <scroll-view scroll-y class="page-content">
      <StationInfo :station="station"></StationInfo>

      <!-- <view class="card real-time-power-card">
        <view class="card__header">
          <view class="card__title-decorator"></view>
          <text class="card__title-text">实时功率</text>
        </view>
        <view class="card__body">
          <view class="power-display">
            <text class="power-display__value">{{ station.power || '0.000' }}</text>
            <text class="power-display__unit">KW</text>
          </view>
          <view class="power-bar">
            <view class="power-bar__inner"></view>
          </view>
          <text class="total-capacity-text">总容量: {{ station.capacity || '0.0000' }} KW</text>
        </view>
      </view> -->

      <view
        v-for="inverter in inverterList"
        :key="inverter.id"
        class="card params-info-card"
        @click="goToInverterDetail(inverter)"
      >
        <view class="card__header">
          <view class="card__title-decorator"></view>
          <text class="card__title-text">逆变器{{ inverter.inverterSn }}</text>
        </view>
        <view class="card__body">
          <view class="info-row">
            <text class="info-row__label">SN码</text>
            <text class="info-row__value">{{ inverter.inverterSn || '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">品牌</text>
            <text class="info-row__value">{{ inverter.brandName || '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">型号</text>
            <text class="info-row__value">{{ inverter.inverterModel || '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">图片</text>
            <wd-img
              v-if="inverter.imageUrl"
              width="50px"
              height="50px"
              radius="4px"
              :src="inverter.imageUrl"
              preview
              :preview-list="[inverter.imageUrl]"
            />
            <text v-else class="info-row__value">-</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "电站详情"
  }
}
</route>

<style scoped lang="scss">
.station-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f5;
}


.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  box-sizing: border-box;
}

.card {
  background-color: #ffffff;
  border-radius: 6px;
  margin-bottom: 15px;
  padding: 17px 18px;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  &__title-decorator {
    width: 3px;
    height: 15px;
    background-color: #37acfe;
    margin-right: 8px;
  }

  &__title-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #4b4b4b;
  }

  &__body {
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #91929e;
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 15px;
    color: #4b4b4b;
    text-align: right;
  }
}

.station-info-card {
  .info-row {
    min-height: 24px;
  }
   .info-row__value {
    max-width: 221px;
  }
}

.real-time-power-card {
  .power-display {
    display: flex;
    align-items: baseline;
    margin-bottom: 10px;

    &__value {
      font-family: 'PingFang SC', sans-serif;
      font-size: 24px;
      color: #303133;
    }

    &__unit {
      font-family: 'PingFang SC', sans-serif;
      font-size: 14px;
      color: #909399;
      margin-left: 5px;
    }
  }

  .power-bar {
    width: 100%;
    height: 6px;
    background-color: #e4e7ed;
    border-radius: 3px;
    margin-bottom: 10px;
    overflow: hidden;

    &__inner {
      width: 10px;
      height: 100%;
      background: linear-gradient(90deg, #409eff, #53a8ff);
      border-radius: 3px;
    }
  }

  .total-capacity-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 12px;
    color: #909399;
  }
}

.params-info-card {
  .info-row {
    min-height: 24px;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }

}

.status-tag {
  padding: 3px 10px;
  border-radius: 11px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 12px;
  min-width: 50px;
  text-align: center;

  &__text {
    line-height: 1;
  }

  &--normal {
    background-color: #f0f9eb;
    .status-tag__text {
      color: #67c23a;
    }
  }

  &--high {
    background-color: #fcf0de;
    .status-tag__text {
      color: #e6a23c;
    }
  }
}

.chart-card {
  padding-bottom: 10px;
}
</style>
