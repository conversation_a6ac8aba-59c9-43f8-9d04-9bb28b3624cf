<script setup lang="ts">
import { ref, computed } from 'vue'
import { watchOnce } from '@vueuse/core'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { baseChartOption } from '@/plugins/echarts'
import type { EChartsOption } from 'echarts/types/dist/shared'
import {
  getInverterDataList,
  getInverterElecData,
  getInverterMpptDataList,
  getInverterWeatherRadiationData,
  updateInverterMpptInfo,
} from '@/api/station'
import type {
  StationInverterRealtimeData,
  StationInverterPowerData,
  InverterMpptData,
  StationWeatherRadiation,
  Inverter,
} from '@/types/api/Station'
import EchartWrapper from '@/components/charts/EchartWrapper.vue'
import { useChannel } from '@/composables/useChannel'
import { useToast } from 'wot-design-uni'
const inverterSn = ref('')
const inverterInfo = ref<Inverter>({})

// MPPT信息相关
interface MpptInfo {
  name: string
  total: number
  pv: Array<{
    name: string
    total: number
  }>
}

const mpptInfoList = ref<MpptInfo[]>([])
const showMpptEditor = ref(false)
const editingMpptList = ref<MpptInfo[]>([]) // 编辑中的MPPT列表

const chartData = ref<StationInverterRealtimeData[]>([])
const elecData = ref<StationInverterPowerData>({})
const mpptData = ref<InverterMpptData[]>([])
const radiationData = ref<StationWeatherRadiation[]>([])

const chartLoading = ref(true)

const inverterStateText = computed(() => {
  const state = elecData.value.inveterState
  if (state === 1)
    return '在线'
  if (state === 2)
    return '离线'
  if (state === 3)
    return '报警'
  return '-'
})

function deepMerge(target: any, source: any) {
  const result = { ...target }
  if (typeof source !== 'object' || source === null) {
    return result
  }
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      const targetValue = result[key]
      const sourceValue = source[key]
      if (
        typeof sourceValue === 'object'
        && sourceValue !== null
        && !Array.isArray(sourceValue)
        && typeof targetValue === 'object'
        && targetValue !== null
        && !Array.isArray(targetValue)
      ) {
        result[key] = deepMerge(targetValue, sourceValue)
      }
      else {
        result[key] = sourceValue
      }
    }
  }
  return result
}

const chartOption = computed<EChartsOption>(() => {
  const dynamicOptions: EChartsOption = {
    legend: {
      data: ['发电功率', '温度'],
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.value.map(d => (d.dataTimestamp ? dayjs(d.dataTimestamp).format('HH:mm') : '')),
    },
    yAxis: [
      {
        type: 'value',
        name: '功率(kW)',
        position: 'left',
      },
      {
        type: 'value',
        name: '温度(°C)',
        position: 'right',
      },
    ],
    series: [
      {
        name: '发电功率',
        type: 'line' as 'line',
        smooth: true,
        data: chartData.value.map(d => d.pac ?? 0),
        yAxisIndex: 0,
      },
      {
        name: '温度',
        type: 'line' as 'line',
        smooth: true,
        data: chartData.value.map(d => d.inverterTemperature ?? 0),
        yAxisIndex: 1,
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
      },
      {
        type: 'slider',
        height: 20,
        bottom: 10,
        start: 0,
        end: 100,
        showDetail: false,
      },
    ],
  }
  return deepMerge(baseChartOption, dynamicOptions)
})

const mpptChartOption = computed<EChartsOption>(() => {
  if (mpptData.value.length === 0)
    return {}

  const mpptGroups = mpptData.value.reduce(
    (acc, cur) => {
      if (!acc[cur.mpptName]) {
        acc[cur.mpptName] = []
      }
      acc[cur.mpptName].push(cur)
      return acc
    },
    {} as Record<string, InverterMpptData[]>,
  )

  const series = Object.keys(mpptGroups).map(mpptName => ({
    name: mpptName,
    type: 'line' as 'line',
    smooth: true,
    data: mpptGroups[mpptName].map(d => d.power),
  }))

  const timestamps = mpptData.value.map(d => dayjs(d.dataTimestamp).format('HH:mm'))
  const uniqueTimestamps = [...new Set(timestamps)]

  const dynamicOptions: EChartsOption = {
    legend: {
      data: Object.keys(mpptGroups),
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: uniqueTimestamps,
    },
    yAxis: {
      type: 'value',
      name: '功率(W)',
    },
    series,
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
      },
      {
        type: 'slider',
        height: 20,
        bottom: 10,
        start: 0,
        end: 100,
        showDetail: false,
      },
    ],
  }

  return deepMerge(baseChartOption, dynamicOptions)
})

const radiationChartOption = computed<EChartsOption>(() => {
  if (radiationData.value.length === 0)
    return {}

  const timestamps = radiationData.value.map(d => dayjs(d.timestamp).format('HH:mm'))

  const dynamicOptions: EChartsOption = {
    legend: {
      data: ['辐射'],
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timestamps,
    },
    yAxis: {
      type: 'value',
      name: '辐射(W/m²)',
    },
    series: [
      {
        name: '辐射',
        type: 'line',
        smooth: true,
        data: radiationData.value.map(d => d.shortwaveRadiation ?? 0),
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
      },
      {
        type: 'slider',
        height: 20,
        bottom: 10,
        start: 0,
        end: 100,
        showDetail: false,
      },
    ],
  }

  return deepMerge(baseChartOption, dynamicOptions)
})

async function fetchData() {
  if (!inverterSn.value)
    return
  chartLoading.value = true
  try {
    const today = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    const params = { inverterSn: inverterSn.value, date: today }

    const [chartRes, elecRes, mpptRes, radiationRes] = await Promise.all([
      getInverterDataList(params),
      getInverterElecData({ inverterSn: inverterSn.value }),
      getInverterMpptDataList(params),
      getInverterWeatherRadiationData(params),
    ])

    chartData.value = chartRes ?? []
    elecData.value = elecRes ?? {}
    mpptData.value = mpptRes ?? []
    radiationData.value = radiationRes ?? []
  }
  catch (error) {
    console.error('Failed to fetch inverter data:', error)
    chartData.value = []
    elecData.value = {}
    mpptData.value = []
    radiationData.value = []
  }
  finally {
    chartLoading.value = false
  }
}

// 解析MPPT信息
function parseMpptInfo(mpptInfoStr?: string): MpptInfo[] {
  if (!mpptInfoStr) return []
  try {
    return JSON.parse(mpptInfoStr)
  } catch (error) {
    console.error('Failed to parse mpptInfo:', error)
    return []
  }
}

// 计算统计信息
const mpptStats = computed(() => {
  const mpptCount = mpptInfoList.value.length
  const stringCount = mpptInfoList.value.reduce((total, mppt) => total + mppt.pv.length, 0)
  const moduleCount = mpptInfoList.value.reduce((total, mppt) =>
    total + mppt.pv.reduce((pvTotal, pv) => pvTotal + pv.total, 0), 0)

  return {
    mpptCount,
    stringCount,
    moduleCount
  }
})

// 开始编辑MPPT信息
function startEditMppt() {
  // 深拷贝当前MPPT信息到编辑列表
  if (mpptInfoList.value.length > 0) {
    editingMpptList.value = JSON.parse(JSON.stringify(mpptInfoList.value))
  } else {
    // 如果没有MPPT数据，创建一个默认的MPPT
    editingMpptList.value = [{
      name: 'mppt1',
      total: 0,
      pv: [{
        name: 'pv1',
        total: 0
      }]
    }]
  }
  showMpptEditor.value = true
}

// 添加新的MPPT
function addMppt() {
  const newMppt: MpptInfo = {
    name: `mppt${editingMpptList.value.length + 1}`,
    total: 0,
    pv: [{
      name: 'pv1',
      total: 0
    }]
  }
  editingMpptList.value.push(newMppt)
}

// 删除MPPT
function removeMppt(index: number) {
  editingMpptList.value.splice(index, 1)
}

// 添加PV组串
function addPv(mpptIndex: number) {
  const mppt = editingMpptList.value[mpptIndex]
  // 计算下一个PV的编号
  const existingNumbers = mppt.pv.map(pv => {
    const match = pv.name.match(/pv(\d+)/)
    return match ? parseInt(match[1]) : 0
  })
  const nextNumber = Math.max(...existingNumbers, 0) + 1

  const newPv = {
    name: `pv${nextNumber}`,
    total: 0
  }
  mppt.pv.push(newPv)
  updateMpptTotal(mpptIndex)
}

// 删除PV组串
function removePv(mpptIndex: number, pvIndex: number) {
  editingMpptList.value[mpptIndex].pv.splice(pvIndex, 1)
  updateMpptTotal(mpptIndex)
}

// 更新MPPT总数
function updateMpptTotal(mpptIndex: number) {
  const mppt = editingMpptList.value[mpptIndex]
  mppt.total = mppt.pv.reduce((sum, pv) => sum + pv.total, 0)
}

// 保存MPPT信息
async function saveMpptInfo() {
  if (!inverterInfo.value.id) {
    toast.error('逆变器ID不存在')
    return
  }

  try {
    // 更新原始数据
    mpptInfoList.value = JSON.parse(JSON.stringify(editingMpptList.value))

    // 调用API保存MPPT信息
    const mpptInfoStr = JSON.stringify(mpptInfoList.value)
    await updateInverterMpptInfo({
      inverterId: inverterInfo.value.id,
      mpptInfo: mpptInfoStr
    })

    toast.success('保存成功')
    showMpptEditor.value = false
  } catch (error) {
    console.error('保存MPPT信息失败:', error)
    toast.error('保存失败，请重试')
  }
}

// 取消编辑
function cancelEdit() {
  editingMpptList.value = []
  showMpptEditor.value = false
}

const { eventChannel } = useChannel()
const toast = useToast()

// 处理接收到的inverter数据
function handleInverterData(data: { inverter: Inverter }) {
  console.log('Received inverter data:', data)
  if (data?.inverter) {
    inverterInfo.value = data.inverter

    // 解析MPPT信息
    if (data.inverter.mpptInfo) {
      mpptInfoList.value = parseMpptInfo(data.inverter.mpptInfo)
    }
  }
}

onLoad((options) => {
  if (options?.inverterSn) {
    inverterSn.value = options.inverterSn
    fetchData()
  }
})

// 监听EventChannel的变化，当EventChannel可用时设置监听器（只执行一次）
watchOnce(eventChannel, (newEventChannel) => {
  if (newEventChannel) {
    newEventChannel.on('receiveInverterData', handleInverterData)
  }
})
</script>

<template>
  <view class="inverter-detail-page">
    <scroll-view scroll-y class="page-content">
      <!-- 逆变器基本信息 -->
      <view v-if="inverterInfo.brandName || inverterInfo.inverterModel || inverterInfo.inverterSn" class="card">
        <view class="card__header">
          <view class="card__title-decorator" />
          <text class="card__title-text">逆变器{{ inverterInfo.inverterSn || '' }}</text>
        </view>
        <view class="card__body">
          <view v-if="inverterInfo.brandName" class="info-row">
            <text class="info-row__label">品牌</text>
            <text class="info-row__value">{{ inverterInfo.brandName }}</text>
          </view>
          <view v-if="inverterInfo.inverterModel" class="info-row">
            <text class="info-row__label">型号</text>
            <text class="info-row__value">{{ inverterInfo.inverterModel || '-' }}</text>
          </view>
          <view v-if="inverterInfo.inverterSn" class="info-row">
            <text class="info-row__label">序列号</text>
            <text class="info-row__value">{{ inverterInfo.inverterSn }}</text>
          </view>
          <view v-if="inverterInfo.status" class="info-row">
            <text class="info-row__label">状态</text>
            <text class="info-row__value">{{ inverterInfo.status === 'ENABLE' ? '启用' : '禁用' }}</text>
          </view>
        </view>
      </view>

      <!-- MPPT统计信息 -->
      <view class="card">
        <view class="card__header">
          <view class="card__title-decorator" />
          <text class="card__title-text">MPPT统计</text>
          <view class="card__header-action">
            <wd-button
              type="primary"
              size="small"
              :round="false"
              @click="startEditMppt"
            >
              {{ mpptStats.mpptCount > 0 ? '编辑' : '新增' }}
            </wd-button>
          </view>
        </view>
        <view class="card__body">
          <view v-if="mpptStats.mpptCount > 0">
            <view class="info-row">
              <text class="info-row__label">MPPT数量</text>
              <text class="info-row__value">{{ mpptStats.mpptCount }}</text>
            </view>
            <view class="info-row">
              <text class="info-row__label">组串数量</text>
              <text class="info-row__value">{{ mpptStats.stringCount }}</text>
            </view>
            <view class="info-row">
              <text class="info-row__label">组串块数</text>
              <text class="info-row__value">{{ mpptStats.moduleCount }}</text>
            </view>
          </view>
          <view v-else class="no-data-tip">
            <text>暂无MPPT数据，点击新增按钮添加</text>
          </view>
        </view>
      </view>

      <!-- MPPT详细信息 -->
      <view v-if="mpptInfoList.length > 0" class="card">
        <view class="card__header">
          <view class="card__title-decorator" />
          <text class="card__title-text">MPPT详情</text>
        </view>
        <view class="card__body">
          <view v-for="(mppt, index) in mpptInfoList" :key="index" class="mppt-item">
            <view class="mppt-header">
              <text class="mppt-name">{{ mppt.name }}</text>
              <text class="mppt-total">总计: {{ mppt.total }}</text>
            </view>
            <view class="pv-list">
              <view v-for="(pv, pvIndex) in mppt.pv" :key="pvIndex" class="pv-item">
                <text class="pv-name">{{ pv.name }}</text>
                <text class="pv-total">{{ pv.total }}块</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="card">
        <view class="card__header">
          <view class="card__title-decorator" />
          <text class="card__title-text">实时状态</text>
        </view>
        <view class="card__body">
          <view class="info-row">
            <text class="info-row__label">实时功率(kW)</text>
            <text class="info-row__value">{{ elecData.pac ?? '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">逆变器状态</text>
            <text class="info-row__value">{{ inverterStateText }}</text>
          </view>
        </view>
      </view>
      <view class="card">
        <view class="card__header">
          <view class="card__title-decorator" />
          <text class="card__title-text">发电量统计</text>
        </view>
        <view class="card__body">
          <view class="info-row">
            <text class="info-row__label">日发电量(kWh)</text>
            <text class="info-row__value">{{ elecData.elecDay ?? '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">月发电量(kWh)</text>
            <text class="info-row__value">{{ elecData.elecMonth ?? '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">年发电量(kWh)</text>
            <text class="info-row__value">{{ elecData.elecYear ?? '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">累计发电量(kWh)</text>
            <text class="info-row__value">{{ elecData.elecTotal ?? '-' }}</text>
          </view>
        </view>
      </view>
      <view class="card chart-card">
        <view class="card__header">
          <view class="card__title-decorator" />
          <text class="card__title-text">MPPT 功率</text>
        </view>
        <view class="card__body">
          <EchartWrapper
            v-if="mpptData.length > 0"
            :options="mpptChartOption"
            :loading="chartLoading"
            height="240px"
          />
          <view v-else class="empty-data">
            <text v-if="chartLoading">加载中...</text>
            <text v-else>暂无数据</text>
          </view>
        </view>
      </view>
      <view class="card chart-card">
        <view class="card__header">
          <view class="card__title-decorator" />
          <text class="card__title-text">辐射度</text>
        </view>
        <view class="card__body">
          <EchartWrapper
            v-if="radiationData.length > 0"
            :options="radiationChartOption"
            :loading="chartLoading"
            height="240px"
          />
          <view v-else class="empty-data">
            <text v-if="chartLoading">加载中...</text>
            <text v-else>暂无数据</text>
          </view>
        </view>
      </view>
      <view class="card chart-card">
        <view class="card__header">
          <view class="card__title-decorator" />
          <text class="card__title-text">发电功率与温度</text>
        </view>
        <view class="card__body">
          <EchartWrapper
            v-if="chartData.length > 0"
            :options="chartOption"
            :loading="chartLoading"
            height="240px"
          />
          <view v-else class="empty-data">
            <text v-if="chartLoading">加载中...</text>
            <text v-else>暂无数据</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- MPPT编辑器弹窗 -->
    <wd-popup v-model="showMpptEditor" position="bottom" :safe-area-inset-bottom="true">
      <view class="mppt-editor">
        <view class="mppt-editor__header">
          <text class="mppt-editor__title">
            {{ mpptInfoList.length > 0 ? '编辑MPPT信息' : '新增MPPT信息' }}
          </text>
          <wd-button type="text" :round="false" @click="cancelEdit">取消</wd-button>
        </view>
        <view class="mppt-editor__content">
          <scroll-view scroll-y class="mppt-editor__scroll">
            <!-- MPPT列表 -->
            <view v-for="(mppt, mpptIndex) in editingMpptList" :key="mpptIndex" class="edit-mppt-item">
              <view class="edit-mppt-header">
                <wd-input
                  v-model="mppt.name"
                  placeholder="MPPT名称"
                  class="mppt-name-input"
                />
                <text class="mppt-total-text">总计: {{ mppt.total }}</text>
                <wd-button
                  type="error"
                  size="small"
                  :round="false"
                  @click="removeMppt(mpptIndex)"
                  :disabled="editingMpptList.length <= 1"
                >
                  删除
                </wd-button>
              </view>

              <!-- PV组串列表 -->
              <view class="pv-edit-list">
                <view v-for="(pv, pvIndex) in mppt.pv" :key="pvIndex" class="pv-edit-item">
                  <wd-input
                    v-model="pv.name"
                    placeholder="PV名称"
                    class="pv-name-input"
                  />
                  <wd-input-number
                    v-model="pv.total"
                    :min="0"
                    :max="100"
                    @change="updateMpptTotal(mpptIndex)"
                    class="pv-total-input"
                  />
                  <wd-button
                    type="error"
                    size="small"
                    :round="false"
                    @click="removePv(mpptIndex, pvIndex)"
                    :disabled="mppt.pv.length <= 1"
                  >
                    删除
                  </wd-button>
                </view>

                <!-- 添加PV按钮 -->
                <wd-button
                  type="primary"
                  size="small"
                  :round="false"
                  @click="addPv(mpptIndex)"
                  class="add-pv-btn"
                >
                  + 添加PV组串
                </wd-button>
              </view>
            </view>

            <!-- 添加MPPT按钮 -->
            <wd-button
              type="primary"
              :round="false"
              @click="addMppt"
              class="add-mppt-btn"
            >
              + 添加MPPT
            </wd-button>
          </scroll-view>
        </view>
        <view class="mppt-editor__footer">
          <wd-button type="primary" block :round="false" @click="saveMpptInfo">保存</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "逆变器详情",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
.inverter-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f5;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  box-sizing: border-box;
}

.card {
  background-color: #ffffff;
  border-radius: 6px;
  margin-bottom: 15px;
  padding: 17px 18px;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  &__title-decorator {
    width: 3px;
    height: 15px;
    background-color: #37acfe;
    margin-right: 8px;
  }

  &__title-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #4b4b4b;
  }

  &__body {
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #91929e;
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 15px;
    color: #4b4b4b;
    text-align: right;
  }
}

.mppt-info-card {
  .info-row {
    min-height: 24px;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.chart-card {
  padding-bottom: 10px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 240px;
  color: #909399;
  font-size: 14px;
}

.card__header-action {
  margin-left: auto;
}

.mppt-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.mppt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .mppt-name {
    font-weight: bold;
    color: #303133;
    font-size: 14px;
  }

  .mppt-total {
    color: #606266;
    font-size: 12px;
  }
}

.pv-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.pv-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #e7f4ff;
  border-radius: 12px;

  .pv-name {
    color: #409eff;
    font-size: 12px;
    margin-right: 4px;
  }

  .pv-total {
    color: #409eff;
    font-size: 12px;
    font-weight: bold;
  }
}

.mppt-editor {
  padding: 20px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    flex-shrink: 0;
  }

  &__title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  &__content {
    flex: 1;
    overflow: hidden;
  }

  &__scroll {
    height: 100%;
    max-height: 50vh;
  }

  &__footer {
    padding-top: 20px;
    flex-shrink: 0;
  }
}

.edit-mppt-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.edit-mppt-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;

  .mppt-name-input {
    flex: 1;
  }

  .mppt-total-text {
    color: #606266;
    font-size: 14px;
    white-space: nowrap;
  }
}

.pv-edit-list {
  .pv-edit-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;

    .pv-name-input {
      flex: 1;
    }

    .pv-total-input {
      width: 100px;
    }
  }

  .add-pv-btn {
    margin-top: 10px;
    width: 100%;
  }
}

.add-mppt-btn {
  margin-top: 20px;
  width: 100%;
}

.no-data-tip {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
}


</style>
