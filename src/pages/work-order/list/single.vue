<script setup lang="ts">
import FilterPopup from './components/FilterPopup.vue'
import { getWorkOrderMySubmitted, getWorkOrderPage } from '@/api/workorder'
import { useUserStore } from '@/store'
import type { WorkOrder, WorkOrderPageParams } from '@/types/api/Workorder'

type TagType = 'primary' | 'success' | 'warning' | 'danger'

const keyword = ref('')
const showFilterPopup = ref(false)
const currentFilters = ref({})

const paging = ref<ZPagingRef>()
const orderList = ref<WorkOrder[]>([])
const pageSize = ref(20)
const userStore = useUserStore()
const userInfo = userStore.userInfo

const apiMap: Record<string, (params: any) => Promise<any>> = {
  MY: getWorkOrderMySubmitted,
  ALL: getWorkOrderPage,
  ASSIGN: getWorkOrderPage,
}

/** ALL, ASSIGN, MY */
const type = ref('ALL')
onLoad((options: any) => {
  type.value = options.type?.toUpperCase() || 'ALL'
})

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: WorkOrderPageParams = {
    pageNum,
    pageSize,
  }

  if (keyword.value) {
    if (/^\d*$/.test(keyword.value)) {
      params.stationCode = keyword.value
    } else {
      params.stationName = keyword.value
    }
  }

  if (type.value === 'ASSIGN') {
    params.orderStatus = 'TO_ASSIGN'
  }

  // 合并筛选条件
  Object.assign(params, currentFilters.value)

  try {
    const res = await apiMap[type.value](params)
    paging.value?.completeByTotal(res.content, res.totalElements)
  } catch {
    paging.value?.complete(false)
  }
}

const handleWorkOrderUpdated = (data: {
  orderCode?: string
  updatedWorkOrder?: WorkOrder
  needsRefresh?: boolean
}) => {
  console.log(data.needsRefresh)
  if (data.needsRefresh) {
    if (paging.value) {
      paging.value.reload()
    }
    return
  }

  if (!data.orderCode || !data.updatedWorkOrder) {
    return
  }

  const { orderCode, updatedWorkOrder } = data
  const index = orderList.value.findIndex((item: WorkOrder) => item.orderCode === orderCode)

  if (index !== -1) {
    let shouldRemainInCurrentTab = true
    if (type.value !== 'ALL') {
      shouldRemainInCurrentTab = false
    }

    if (shouldRemainInCurrentTab) {
      orderList.value.splice(index, 1, updatedWorkOrder)
    } else {
      if (orderList.value.length === 1 && paging.value) {
        paging.value.reload()
      } else {
        orderList.value.splice(index, 1)
      }
    }
  } else if (paging.value) {
    paging.value.reload()
  }
}

const goToDetail = (order: WorkOrder) => {
  let action = 'approve'
  if (['FINISHED', 'CLOSED'].includes(order.orderStatus!)) {
    action = 'view'
  } else if (order.orderStatus === 'TO_PROCESS') {
    action = 'handle'
  } else if (order.orderStatus === 'TO_ASSIGN') {
    if (userInfo?.userType === 'merchant' && userInfo?.isProvider === true) {
      action = 'assign'
    } else {
      action = 'view'
    }
  } else if (order.orderStatus === 'TO_SUB_CENTER_DISPATCH') {
    if (userInfo?.userType === 'haier' && userInfo?.subCenterUser === false) {
      action = 'view'
    }
  } else if (order.orderStatus === 'TO_HEAD_DISPATCH') {
    if (userInfo?.userType === 'haier' && userInfo?.subCenterUser === true) {
      action = 'view'
    }
  } else if (order.orderStatus === 'TO_SUB_CENTER_AUDIT') {
    if (userInfo?.userType === 'haier' && userInfo?.subCenterUser === false) {
      action = 'view'
    }
  } else if (order.orderStatus === 'TO_HEAD_AUDIT') {
    if (userInfo?.userType === 'haier' && userInfo?.subCenterUser === true) {
      action = 'view'
    }
  }
  const fromQuery = type.value === 'MY' ? '&from=my' : ''
  const url = `/pages/work-order/index?orderCode=${order.orderCode}&action=${action}${fromQuery}`
  uni.navigateTo({
    url,
    events: {
      workOrderUpdated: handleWorkOrderUpdated,
    },
  })
}

const goToCreate = () => {
  uni.navigateTo({
    url: `/pages/work-order/create`,
    events: {
      workOrderUpdated: handleWorkOrderUpdated,
    },
  })
}

const getOrderStatusText = (status?: string) => {
  if (!status) {
    return ''
  }
  const statusMap: Record<string, string> = {
    TO_HEAD_DISPATCH: '待总部下发',
    TO_SUB_CENTER_DISPATCH: '待分中心下发',
    TO_ASSIGN: '待指派',
    TO_PROCESS: '待处理',
    HANDLED: '已处理',
    TO_SUB_CENTER_AUDIT: '待总部审核',
    TO_HEAD_AUDIT: '待分中心审核',
    FINISHED: '已完成',
    CLOSED: '已关单',
  }
  return statusMap[status] || status
}

const handleApplyFilter = (filters: any) => {
  currentFilters.value = filters
  showFilterPopup.value = false
  paging.value?.reload()
}
</script>

<template>
  <view class="work-order-list-page">
    <!-- 搜索区域 -->
    <SearchBar
      v-model="keyword"
      :show-filter="true"
      @search="onSearch"
      @filter="showFilterPopup = true"
      placeholder="请输入电站编号/电站名称"
    />

    <!-- 工单列表 -->
    <z-paging
      ref="paging"
      v-model="orderList"
      class="order-list-paging"
      :fixed="false"
      @query="queryList"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
    >
      <view class="order-list-content">
        <view
          v-for="order in orderList"
          :key="order.id"
          class="order-card"
          @click="goToDetail(order)"
        >
          <view class="card-header">
            <text class="title">{{ order.faultDescription }}</text>
            <wd-tag type="primary">{{
              getOrderStatusText(order.orderStatus)
            }}</wd-tag>
          </view>
          <view class="card-meta">
            <text>{{ order.createdAt }}</text>
          </view>
          <view class="card-body">
            <view class="detail-item">
              <text class="label">电站编码</text>
              <text class="value">{{ order.stationCode }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站名称</text>
              <text class="value">{{ order.stationName }}</text>
            </view>
            <view class="detail-item">
              <text class="label">联系方式</text>
              <text class="value">{{ order.stationPhone }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站地址</text>
              <text class="value">{{ order.address }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
    <wd-fab
      v-if="type === 'MY'"
      position="right-bottom"
      :z-index="5"
      :draggable="true"
      :expandable="false"
      @click="goToCreate"
    />
    <FilterPopup v-model:modelValue="showFilterPopup" @apply="handleApplyFilter" />
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "工单列表"
  }
}
</route>

<style scoped lang="scss">
.work-order-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .order-list-paging {
    flex: 1;
  }

  .order-list-content {
    padding: 8px 12px;
  }

  .order-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        flex: 1;
        margin-right: 4px;
      }
    }

    .card-meta {
      font-size: 12px;
      color: #999;
      margin-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }

    .card-body {
      .detail-item {
        display: flex;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 4px;

        .label {
          color: #666;
          margin-right: 8px;
          flex-shrink: 0;
          width: 70px;
        }

        .value {
          color: #333;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin-bottom: 0px;
    }
  }
}
</style>
