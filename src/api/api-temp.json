{
  "swagger": "2.0",
  "info": {
    "description": "外部系统调用网关",
    "version": "1.0",
    "title": "日日顺乐农商户平台网关API接口",
    "contact": {
      "name": "rrsjk",
      "url": "http://www.rrsjk.com",
      "email": "<EMAIL>"
    }
  },
  "host": "operation.xiaoxianglink.com",
  "basePath": "/hdsapi",
  "paths": {
    "/light/operation/station/inverter/mppt/update":{"post":{"tags":["光伏运维/电站管理"],"summary":"更新逆变器MPPT信息","operationId":"updateInverterMpptInfoUsingPOST","consumes":["application/json"],"produces":["*/*"],"parameters":[{"name":"Authorization","in":"header","description":"访问令牌,格式Bearer ${access_token}","required":false,"type":"string"},{"in":"body","name":"req","description":"req","required":true,"schema":{"$ref":"#/definitions/InverterUpdateReq"}}],"responses":{"200":{"description":"OK","schema":{"type":"boolean"}},"201":{"description":"Created"},"401":{"description":"Unauthorized"},"403":{"description":"Forbidden"},"404":{"description":"Not Found"}},"deprecated":false}},"/light/operation/station/inverter/weather-radiation-data":{"get":{"tags":["光伏运维/电站管理"],"summary":"获取逆变器天气辐射数据","operationId":"getInverterWeatherRadiationDataUsingGET","produces":["*/*"],"parameters":[{"name":"Authorization","in":"header","description":"访问令牌,格式Bearer ${access_token}","required":false,"type":"string"},{"name":"date","in":"query","description":"查询日期","required":false,"type":"string"},{"name":"inverterSn","in":"query","description":"逆变器SN","required":false,"type":"string"}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/WeatherRadiationDto"}},"401":{"description":"Unauthorized"},"403":{"description":"Forbidden"},"404":{"description":"Not Found"}},"deprecated":false}},"/light/operation/station/location/update":{"post":{"tags":["光伏运维/电站管理"],"summary":"更新电站经纬度","operationId":"updateStationLocationUsingPOST","consumes":["application/json"],"produces":["*/*"],"parameters":[{"name":"Authorization","in":"header","description":"访问令牌,格式Bearer ${access_token}","required":false,"type":"string"},{"in":"body","name":"req","description":"req","required":true,"schema":{"$ref":"#/definitions/StationUpdateReq"}}],"responses":{"200":{"description":"OK","schema":{"type":"boolean"}},"201":{"description":"Created"},"401":{"description":"Unauthorized"},"403":{"description":"Forbidden"},"404":{"description":"Not Found"}},"deprecated":false}},
  },
  "InverterUpdateReq": {
    "type": "object",
    "required": [
      "inverterId",
      "mpptInfo"
    ],
    "properties": {
      "inverterId": {
        "type": "integer",
        "format": "int64",
        "description": "逆变器ID"
      },
      "mpptInfo": {
        "type": "string",
        "description": "MPPT信息(JSON字符串)"
      }
    },
    "title": "InverterUpdateReq",
    "description": "逆变器MPPT信息更新请求"
  },
  "WeatherRadiationDto": {
    "type": "object",
    "properties": {
      "shortwaveRadiation": {
        "type": "number",
        "description": "短波辐射(W/m²)"
      },
      "timestamp": {
        "type": "string",
        "format": "date-time",
        "description": "数据时间戳"
      }
    },
    "title": "WeatherRadiationDto",
    "description": "天气辐射数据"
  },
  "StationUpdateReq": {
    "type": "object",
    "required": [
      "newLatitude",
      "newLongitude",
      "stationCode"
    ],
    "properties": {
      "newLatitude": {
        "type": "number",
        "description": "新纬度"
      },
      "newLongitude": {
        "type": "number",
        "description": "新经度"
      },
      "stationCode": {
        "type": "string",
        "description": "电站编码"
      }
    },
    "title": "StationUpdateReq",
    "description": "电站经纬度更新请求参数"
  }
}
